# Legal Hebrew Nexus - Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Application Configuration
VITE_APP_URL=https://your-domain.com
VITE_ENVIRONMENT=development

# Analytics Configuration
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SPEED_INSIGHTS=true

# Version Management
VITE_ENABLE_AUTO_REFRESH=true

# Optional: Additional Analytics Services
VITE_SENTRY_DSN=your_sentry_dsn
VITE_GA_TRACKING_ID=your_google_analytics_id

# Development Settings
# Set to 'false' to disable features in development
# VITE_ENABLE_ANALYTICS=false
# VITE_ENABLE_SPEED_INSIGHTS=false
# VITE_ENABLE_AUTO_REFRESH=false
