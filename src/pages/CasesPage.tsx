import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Eye,
  Calendar,
  User,
  FileText,
  Loader2,
  Trash2,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Clock,
  MessageSquare,
  Phone,
  CheckCircle,
  PlayCircle
} from "lucide-react";
import { useCases } from "@/hooks/useCases";
import { CaseModal } from "@/components/case/CaseModal";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { useUserManagement } from "@/hooks/useUserManagement";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

const CasesPage = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const { cases, caseTypes, isLoading, deleteCase } = useCases();
  const { users } = useUserManagement();

  const filteredCases = useMemo(() => {
    return cases.filter(case_ => {
      const matchesSearch = !searchTerm || 
        case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        case_.lead?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        case_.lead?.phone?.includes(searchTerm);
      
      const matchesStatus = statusFilter === "all" || case_.status === statusFilter;
      const matchesType = typeFilter === "all" || case_.case_type_id === typeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [cases, searchTerm, statusFilter, typeFilter]);

  const casesByStatus = useMemo(() => {
    const grouped = {
      "בקליטה": filteredCases.filter(c => c.status === "בקליטה"),
      "פתוח": filteredCases.filter(c => c.status === "פתוח"),
      "סגור": filteredCases.filter(c => c.status === "סגור"),
      "כל התיקים": filteredCases
    };
    return grouped;
  }, [filteredCases]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "בקליטה": return "secondary";
      case "פתוח": return "default";
      case "סגור": return "outline";
      default: return "secondary";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "בקליטה": return "bg-purple-500";
      case "פתוח": return "bg-green-500";
      case "סגור": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  const handleDeleteCase = async (caseId: string, caseName: string) => {
    if (confirm(`האם אתה בטוח שברצונך למחוק את התיק "${caseName}"?`)) {
      await deleteCase(caseId);
    }
  };

  const handleQuickStatusChange = async (caseId: string, newStatus: string) => {
    try {
      // TODO: Implement quick status change
      console.log(`Changing case ${caseId} to status ${newStatus}`);
    } catch (error) {
      console.error('Error changing case status:', error);
    }
  };

  const CasesTable = ({ cases, showStatus = true }: { cases: any[], showStatus?: boolean }) => (
    <div className="card-professional rounded-lg overflow-hidden">
      <table className="professional-table">
        <thead>
          <tr>
            <th className="text-center">פעולות</th>
            <th className="text-center">ערך/זמן</th>
            <th className="text-center">דדליין</th>
            <th className="text-center">סוג התיק</th>
            {showStatus && <th className="text-center">סטטוס</th>}
            <th className="text-center">משתמש מוקצה</th>
            <th className="text-center">לקוח</th>
            <th className="text-center">שם התיק</th>
          </tr>
        </thead>
        <tbody>
          {cases.map((case_) => (
            <tr
              key={case_.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(`/case/${case_.id}`)}
            >
              <td className="text-center">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => navigate(`/case/${case_.id}`)}>
                      <Eye className="h-4 w-4 mr-2" />
                      צפה בתיק
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate(`/case/${case_.id}#time-tracking`)}>
                      <Clock className="h-4 w-4 mr-2" />
                      רישום זמן
                    </DropdownMenuItem>
                    {case_.lead?.phone && (
                      <DropdownMenuItem onClick={() => window.open(`https://wa.me/${case_.lead.phone.replace(/[^0-9]/g, '')}`, '_blank')}>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        שלח וואטסאפ
                      </DropdownMenuItem>
                    )}
                    {case_.lead?.phone && (
                      <DropdownMenuItem onClick={() => window.open(`tel:${case_.lead.phone}`, '_self')}>
                        <Phone className="h-4 w-4 mr-2" />
                        התקשר ללקוח
                      </DropdownMenuItem>
                    )}
                    {case_.status !== 'סגור' && (
                      <DropdownMenuItem onClick={() => {/* TODO: Quick close case */}}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        סגור תיק
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => handleDeleteCase(case_.id, case_.title)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      מחק תיק
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
              <td className="text-sm text-center">
                <div className="space-y-1">
                  {case_.value && (
                    <div className="case-value">
                      ₪{case_.value.toLocaleString()}
                    </div>
                  )}
                  {case_.total_time_logged > 0 && (
                    <div className="hours-logged">
                      {Math.floor(case_.total_time_logged / 60)}:{(case_.total_time_logged % 60).toString().padStart(2, '0')} שעות
                    </div>
                  )}
                  {!case_.value && !case_.total_time_logged && (
                    <div className="text-muted-foreground">-</div>
                  )}
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  {case_.deadline ? new Date(case_.deadline).toLocaleDateString('he-IL') : "לא צוין"}
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <FileText className="w-4 h-4 text-muted-foreground" />
                  {case_.case_type?.name || "לא צוין"}
                </div>
              </td>
              {showStatus && (
                <td className="text-center">
                  <Badge variant={getStatusBadgeVariant(case_.status)}>
                    {case_.status}
                  </Badge>
                </td>
              )}
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.assigned_user_id
                    ? users.find(u => u.id === case_.assigned_user_id)?.full_name ||
                      users.find(u => u.id === case_.assigned_user_id)?.email ||
                      "משתמש לא נמצא"
                    : "לא מוקצה"
                  }
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.lead?.full_name || "לא צוין"}
                </div>
              </td>
              <td className="font-medium text-center">{case_.title}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>טוען תיקים...</span>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">ניהול תיקים</h1>
            <p className="text-muted-foreground">ניהול כל התיקים במערכת</p>
          </div>
          <div className="flex gap-2">
            <Button
              className="btn-professional flex items-center gap-2"
              onClick={() => setIsModalOpen(true)}
            >
              <Plus className="w-4 h-4" />
              צור תיק חדש
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => navigate('/office/leads')}
            >
              <User className="w-4 h-4" />
              מלידים
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              סינון וחיפוש
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="חפש תיק, לקוח או טלפון..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="סינון לפי סטטוס" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">כל הסטטוסים</SelectItem>
                  <SelectItem value="בקליטה">בקליטה</SelectItem>
                  <SelectItem value="פתוח">פתוח</SelectItem>
                  <SelectItem value="סגור">סגור</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="סינון לפי סוג תיק" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">כל סוגי התיקים</SelectItem>
                  {caseTypes.map(type => (
                    <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Cases Tabs */}
        <Tabs defaultValue="כל התיקים" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {Object.entries(casesByStatus).map(([status, statusCases]) => (
              <TabsTrigger key={status} value={status} className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${getStatusColor(status)}`}></div>
                {status} ({statusCases.length})
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(casesByStatus).map(([status, statusCases]) => (
            <TabsContent key={status} value={status}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
                    {status} ({statusCases.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {statusCases.length > 0 ? (
                    <CasesTable cases={statusCases} showStatus={status === "כל התיקים"} />
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      {searchTerm || statusFilter !== "all" || typeFilter !== "all" 
                        ? "לא נמצאו תיקים התואמים לחיפוש"
                        : `אין תיקים ב${status === "כל התיקים" ? "מערכת" : status} כרגע`
                      }
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        <CaseModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          caseTypes={caseTypes}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </ErrorBoundary>
  );
};

export default CasesPage;
